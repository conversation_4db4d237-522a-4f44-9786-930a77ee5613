// Styling for the linked sites grid in anomaly section
.linked-sites-anomaly-grid {
  .k-grid-header {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
  }

  .k-grid-content {
    max-height: 400px;
    overflow-y: auto;
  }

  .dropdown-menu-actions {
    min-width: 200px;

    .dropdown-item {
      padding: 8px 16px;
      font-size: 0.875rem;

      &:hover {
        background-color: #f8f9fa;
      }

      i {
        width: 16px;
        text-align: center;
      }
    }
  }

  .field-value-cell {
    .text-truncate {
      max-width: 150px;
    }

    .btn-sm {
      padding: 2px 6px;
      font-size: 0.75rem;
    }
  }
}

// Badge styling for active status
.badge {
  &.bg-success {
    background-color: #198754 !important;
  }

  &.bg-danger {
    background-color: #dc3545 !important;
  }
}

// Alert styling for anomaly sections
.alert {
  &.alert-warning {
    border-left: 4px solid #ffc107;
  }
}

// Loading spinner styling
.spinner-border {
  width: 2rem;
  height: 2rem;
}